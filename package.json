{"name": "sinak-pwa", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "dev:emulator": "concurrently \"firebase emulators:start --only firestore,auth\" \"vite\"", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "emulator:start": "firebase emulators:start --only firestore,auth", "emulator:ui": "firebase emulators:start --only firestore,auth,ui", "emulator:export": "firebase emulators:export ./emulator-data", "emulator:import": "firebase emulators:start --import=./emulator-data --only firestore,auth", "test": "jest", "test:emulator": "firebase emulators:exec --only firestore,auth 'npm test'", "test:offline": "VITE_DISABLE_FIRESTORE=true npm test"}, "dependencies": {"firebase": "^10.12.5", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.28.0", "@rushstack/eslint-patch": "^1.3.3", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/vue3-jest": "^29.2.6", "babel-jest": "^29.7.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "prettier": "^3.0.3", "vite": "^5.1.6"}}