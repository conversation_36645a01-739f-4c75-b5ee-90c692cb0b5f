{"name": "sinak-pwa", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"firebase": "^10.12.5", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3", "vite": "^5.1.6"}}