<script setup>
import { RouterView, useRoute } from 'vue-router'
import { computed, watch, ref } from 'vue'
import ConnectionStatus from './components/common/ConnectionStatus.vue'
import PageTransition from './components/transitions/PageTransition.vue'

const route = useRoute()
const transitionName = ref('fade')

// Determine transition based on route navigation
const getTransitionName = (to, from) => {
  if (!from.name) return 'fade'

  // Define route hierarchy for smart transitions
  const routeHierarchy = {
    'Login': 0,
    'Register': 0,
    'Dashboard': 1,
    'Diagnosis': 2,
    'Recommendations': 3
  }

  const toLevel = routeHierarchy[to.name] || 1
  const fromLevel = routeHierarchy[from.name] || 1

  if (toLevel > fromLevel) {
    return 'slide-left' // Moving forward
  } else if (toLevel < fromLevel) {
    return 'slide-right' // Moving backward
  } else {
    return 'fade' // Same level
  }
}

// Watch route changes to update transition
watch(route, (to, from) => {
  transitionName.value = getTransitionName(to, from)
}, { flush: 'pre' })
</script>

<template>
  <div id="app">
    <ConnectionStatus />

    <!-- Page Transition Wrapper -->
    <PageTransition
      :name="transitionName"
      mode="out-in"
      :duration="300"
    >
      <RouterView :key="route.path" />
    </PageTransition>
  </div>
</template>

<style scoped>
</style>
