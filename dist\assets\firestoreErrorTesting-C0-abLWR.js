import{t as T,u as d,a as R,b as w}from"./index-BsEZ7LlR.js";const m=async(r="ZfKRfbByUmO53hVXGaLRZC0GuHB2")=>{console.log("🧪 Testing Internal Assertion Error Scenario..."),console.log("=".repeat(60));const e={testName:"Firestore Internal Assertion Error Resolution",userId:r,steps:[],success:!1,errors:[],recommendations:[]};try{console.log("📡 Step 1: Testing Firestore connectivity...");const o=await T();if(e.steps.push({step:1,name:"Firestore Connectivity",success:o,details:o?"Connection successful":"Connection failed"}),!o)return e.errors.push("Firestore connectivity test failed"),e;console.log("📝 Step 2: Testing simple user document update...");const i={testUpdate:!0,timestamp:new Date().toISOString(),testType:"simple_update"},a=await d(r,i);e.steps.push({step:2,name:"Simple User Document Update",success:a,details:a?"Simple update successful":"Simple update failed"}),console.log("📊 Step 3: Testing recommendation data update...");const n={recommendations:[{id:"test-rec-1",title:"Test Recommendation 1",description:"This is a test recommendation to simulate the error scenario",category:"growth_strategy",priority:"high",progressSteps:[{id:"step-1",title:"Test Step 1",description:"Test step description",order:0,isRequired:!0,status:"pending"}],milestones:[{id:"milestone-1",title:"Test Milestone",description:"Test milestone description",targetDate:new Date(Date.now()+30*24*60*60*1e3).toISOString().split("T")[0]}],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}],businessProfile:{userId:r,businessName:"Test Business",category:"food_beverage",stage:"survival",location:"Jakarta",updatedAt:new Date().toISOString()},analytics:{totalRecommendations:1,completedRecommendations:0,lastGeneratedAt:new Date().toISOString()}},c=await R(r,n);e.steps.push({step:3,name:"Recommendation Data Update",success:c,details:c?"Recommendation update successful":"Recommendation update failed"}),console.log("⚡ Step 4: Testing rapid consecutive updates...");const t=[];for(let s=0;s<5;s++){const p={rapidTest:!0,updateNumber:s+1,timestamp:new Date().toISOString()};t.push(d(r,p))}const l=(await Promise.allSettled(t)).filter(s=>s.status==="fulfilled"&&s.value===!0).length;e.steps.push({step:4,name:"Rapid Consecutive Updates",success:l>0,details:`${l}/5 rapid updates successful`}),console.log("🏪 Step 5: Testing recommendation store save mechanism...");try{const s=w();s.businessProfile=n.businessProfile,s.recommendations=n.recommendations,s.analytics=n.analytics,await s.safeSaveRecommendations(r),e.steps.push({step:5,name:"Recommendation Store Save",success:!0,details:"Store save mechanism completed without errors"})}catch(s){e.steps.push({step:5,name:"Recommendation Store Save",success:!1,details:`Store save error: ${s.message}`}),e.errors.push(`Store save error: ${s.message}`)}const f=e.steps.filter(s=>s.success).length,S=e.steps.length;e.success=f===S,console.log(`
`+"=".repeat(60)),console.log("📊 TEST RESULTS SUMMARY"),console.log("=".repeat(60)),console.log(`✅ Successful Steps: ${f}/${S}`),e.success?console.log("🎉 All tests passed! Internal assertion error appears to be resolved."):(console.log("⚠️ Some tests failed. Check individual step results."),e.errors.forEach(s=>console.log(`   ❌ ${s}`))),e.steps.forEach(s=>{const p=s.success?"✅":"❌";console.log(`${p} Step ${s.step}: ${s.name} - ${s.details}`)})}catch(o){console.error("❌ Test suite error:",o),e.errors.push(`Test suite error: ${o.message}`),e.success=!1}return e},g=async(r="ZfKRfbByUmO53hVXGaLRZC0GuHB2")=>{console.log("🧪 Testing Concurrent Write Operations...");const e={testName:"Concurrent Write Operations Test",userId:r,concurrentOperations:10,results:[],success:!1};try{const o=[];for(let t=0;t<e.concurrentOperations;t++){const u={concurrentTest:!0,operationId:t+1,timestamp:new Date().toISOString(),randomData:Math.random().toString(36).substring(7)};o.push(d(r,u).then(l=>({operationId:t+1,success:l,error:null})).catch(l=>({operationId:t+1,success:!1,error:l.message})))}const i=await Promise.all(o);e.results=i;const a=i.filter(t=>t.success).length,n=i.filter(t=>!t.success).length;e.success=a>0,console.log("📊 Concurrent Operations Results:"),console.log(`   ✅ Successful: ${a}/${e.concurrentOperations}`),console.log(`   ❌ Failed: ${n}/${e.concurrentOperations}`);const c=i.filter(t=>t.error);c.length>0&&(console.log("🔍 Errors encountered:"),c.forEach(t=>{console.log(`   Operation ${t.operationId}: ${t.error}`)}))}catch(o){console.error("❌ Concurrent write test error:",o),e.success=!1}return e},h=async(r="ZfKRfbByUmO53hVXGaLRZC0GuHB2")=>{console.log("🚀 Running Comprehensive Firestore Error Resolution Tests..."),console.log("=".repeat(80));const e=[];console.log(`
🧪 Test 1: Internal Assertion Error Scenario`);const o=await m(r);e.push(o),console.log(`
🧪 Test 2: Concurrent Write Operations`);const i=await g(r);e.push(i),console.log(`
`+"=".repeat(80)),console.log("🎯 COMPREHENSIVE TEST SUMMARY"),console.log("=".repeat(80));const a=e.filter(c=>c.success).length,n=e.length;return console.log(`📊 Overall Results: ${a}/${n} test suites passed`),e.forEach((c,t)=>{const u=c.success?"✅":"❌";console.log(`${u} Test ${t+1}: ${c.testName}`)}),a===n?(console.log(`
🎉 All Firestore error resolution tests passed!`),console.log("✅ Internal assertion error appears to be resolved")):console.log(`
⚠️ Some tests failed - check individual results above`),{summary:{total:n,passed:a,failed:n-a,success:a===n},results:e}};typeof window<"u"&&(window.testInternalAssertionScenario=m,window.testConcurrentWrites=g,window.runFirestoreErrorTests=h,console.log("🧪 Firestore error testing functions available:"),console.log("- testInternalAssertionScenario(userId)"),console.log("- testConcurrentWrites(userId)"),console.log("- runFirestoreErrorTests(userId)"));const y={testInternalAssertionScenario:m,testConcurrentWrites:g,runFirestoreErrorTests:h};export{y as default,h as runFirestoreErrorTests,g as testConcurrentWrites,m as testInternalAssertionScenario};
