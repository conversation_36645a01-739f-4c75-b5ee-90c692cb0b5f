# Firebase Configuration
# Copy this file to .env.local and fill in your Firebase project details

VITE_FIREBASE_API_KEY="AIzaSyBhZvHQT5Omr8L_O9sn-BFrni6GzPu4pqU",
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Optional: Use Firebase Emulator for development
# VITE_USE_FIREBASE_EMULATOR=true

# Firebase AI Configuration
# AI is automatically configured through Firebase - no additional API keys needed
# Gemini API key is managed securely by Firebase

# Application Configuration
VITE_APP_NAME=SiNaik PWA
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# Feature Flags
VITE_ENABLE_AI_RECOMMENDATIONS=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_NOTIFICATIONS=true
