import{c as g}from"./index-BsEZ7LlR.js";const u={warung:{userId:"test-warung-001",businessName:"Warung Makan Bu Sari",category:"food_beverage",stage:"survival",location:"Jakarta Selatan",employeeCount:2,monthlyRevenue:15e6,challenges:["Persaingan ketat","Modal terbatas","Manajemen keuangan"],goals:["Meningkatkan penjualan","Memperluas menu","Digitalisasi"],description:"Warung makan tradisional yang menyajikan masakan rumahan dengan 2 karyawan"},fashion:{userId:"test-fashion-001",businessName:"Hijab Cantik Store",category:"fashion_retail",stage:"success",location:"Bandung",employeeCount:5,monthlyRevenue:45e6,challenges:["Inventory management","Customer retention","Kompetisi online"],goals:["Ekspansi ke marketplace","Brand awareness","Otomasi proses"],description:"Toko online hijab dan fashion muslim dengan fokus kualitas premium"},tech:{userId:"test-tech-001",businessName:"EduTech Indonesia",category:"technology",stage:"take_off",location:"Jakarta",employeeCount:15,monthlyRevenue:12e7,challenges:["Scaling team","Product development","Market penetration"],goals:["Series A funding","User acquisition","Product expansion"],description:"Platform edukasi online untuk pelajar SMA dengan fitur AI tutoring"},craft:{userId:"test-craft-001",businessName:"Kerajinan Bambu Nusantara",category:"handicraft",stage:"existence",location:"Yogyakarta",employeeCount:3,monthlyRevenue:8e6,challenges:["Akses pasar","Digitalisasi","Standarisasi produk"],goals:["Online presence","Export market","Skill development"],description:"Usaha kerajinan bambu tradisional dengan teknik turun temurun"}},d={warung:{strengths:["Lokasi strategis","Rasa masakan enak","Pelanggan loyal"],weaknesses:["Tidak ada sistem POS","Pencatatan manual","Tidak ada online presence"],opportunities:["Delivery online","Catering service","Menu diet sehat"],threats:["Kompetitor franchise","Kenaikan harga bahan","Regulasi kesehatan"],currentChallenges:["Cash flow tidak stabil","Sulit tracking inventory","Customer acquisition terbatas"],businessGoals:["Omzet naik 30%","Sistem keuangan digital","Ekspansi delivery"],marketPosition:"Local favorite dengan potensi digital growth",competitiveAdvantage:"Rasa autentik dan harga terjangkau"},fashion:{strengths:["Brand recognition","Quality products","Social media presence"],weaknesses:["Limited inventory system","Manual order processing","No customer analytics"],opportunities:["Marketplace expansion","Influencer partnerships","International market"],threats:["Fast fashion competition","Economic downturn","Changing trends"],currentChallenges:["Inventory optimization","Customer lifetime value","Operational efficiency"],businessGoals:["Revenue growth 50%","Automated systems","Brand expansion"],marketPosition:"Premium hijab brand with loyal customer base",competitiveAdvantage:"High quality materials and exclusive designs"},tech:{strengths:["Innovative product","Strong team","Early traction"],weaknesses:["Limited funding","Scalability challenges","Market education needed"],opportunities:["Government digitalization push","Remote learning trend","AI advancement"],threats:["Big tech competition","Regulatory changes","Economic uncertainty"],currentChallenges:["User acquisition cost","Product-market fit","Team scaling"],businessGoals:["10x user growth","Funding round","Product expansion"],marketPosition:"Emerging player in EdTech with unique AI features",competitiveAdvantage:"AI-powered personalized learning"},craft:{strengths:["Traditional skills","Unique products","Sustainable materials"],weaknesses:["Limited market reach","No digital presence","Inconsistent quality"],opportunities:["Export market","Eco-friendly trend","Tourism recovery"],threats:["Mass production competition","Raw material scarcity","Skill transfer gap"],currentChallenges:["Market access","Quality standardization","Digital adoption"],businessGoals:["Online sales channel","Export readiness","Skill certification"],marketPosition:"Traditional craft with modern market potential",competitiveAdvantage:"Authentic traditional techniques and sustainable practices"}},m=(a,n=[])=>{const e={isValid:!0,errors:[],warnings:[],score:0,details:{}};try{const i=typeof a=="string"?JSON.parse(a):a;if(!i.recommendations||!Array.isArray(i.recommendations))return e.errors.push("Missing or invalid recommendations array"),e.isValid=!1,e;const s=i.recommendations;e.details.recommendationCount=s.length,s.forEach((o,r)=>{const t=p(o,r);t.isValid||(e.errors.push(...t.errors),e.isValid=!1),e.warnings.push(...t.warnings)}),i.summary&&(e.details.hasSummary=!0,i.summary.totalRecommendations!==s.length&&e.warnings.push("Summary count mismatch with actual recommendations")),e.score=h(i)}catch(i){e.errors.push(`JSON parsing error: ${i.message}`),e.isValid=!1}return e},p=(a,n)=>{const e={isValid:!0,errors:[],warnings:[]};return["title","description","category","priority"].forEach(s=>{a[s]||(e.errors.push(`Recommendation ${n+1}: Missing required field '${s}'`),e.isValid=!1)}),a.title&&a.title.length<10&&e.warnings.push(`Recommendation ${n+1}: Title too short`),a.description&&a.description.length<50&&e.warnings.push(`Recommendation ${n+1}: Description too short`),a.progressSteps&&Array.isArray(a.progressSteps)&&a.progressSteps.forEach((s,o)=>{(!s.title||!s.description)&&e.warnings.push(`Recommendation ${n+1}, Step ${o+1}: Missing title or description`)}),a.milestones&&Array.isArray(a.milestones)&&a.milestones.forEach((s,o)=>{(!s.title||!s.description)&&e.warnings.push(`Recommendation ${n+1}, Milestone ${o+1}: Missing title or description`)}),e},h=a=>{let n=0;const e=a.recommendations;n+=20;const i=e.length;i>=6&&i<=10?n+=15:i>=4&&i<=12?n+=10:n+=5;let s=0;e.forEach(t=>{t.title&&t.title.length>=15&&t.title.length<=80&&(s+=2),t.description&&t.description.length>=100&&(s+=3),t.progressSteps&&t.progressSteps.length>=2&&(s+=5),t.milestones&&t.milestones.length>=1&&(s+=3),t.description&&(t.description.includes("UMKM")||t.description.includes("Indonesia")||t.description.includes("Rupiah")||t.description.includes("regulasi"))&&(s+=2)}),n+=Math.min(s,40),a.summary&&(n+=10);const o=new Set(e.map(t=>t.category)),r=new Set(e.map(t=>t.priority));return n+=Math.min(o.size*2,10),n+=Math.min(r.size*2,5),Math.min(n,100)},f=(a="warung")=>{const n=u[a],e=d[a];if(!n||!e)throw new Error(`Test profile '${a}' not found`);return g(n,e)},k=async(a,n=3)=>{const e=[];for(let r=0;r<n;r++){const t=performance.now();try{const l=await a(),c=performance.now();e.push({iteration:r+1,success:!0,duration:c-t,result:l,validation:m(l)})}catch(l){const c=performance.now();e.push({iteration:r+1,success:!1,duration:c-t,error:l.message,validation:null})}}const i=e.filter(r=>r.success),s=i.reduce((r,t)=>r+t.duration,0)/i.length,o=i.reduce((r,t)=>{var l;return r+(((l=t.validation)==null?void 0:l.score)||0)},0)/i.length;return{totalIterations:n,successfulIterations:i.length,successRate:i.length/n*100,averageDuration:s,averageQualityScore:o,results:e}},y={profiles:u,diagnosis:d,validate:m,generateContext:f,measurePerformance:k};typeof window<"u"&&(window.AITestUtils=y);export{y as AITestUtils,u as TEST_BUSINESS_PROFILES,d as TEST_DIAGNOSIS_DATA,f as generateTestContext,k as measureAIPerformance,m as validateAIResponse};
