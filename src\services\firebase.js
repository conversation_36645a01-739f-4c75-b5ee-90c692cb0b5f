// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
// Firebase AI not available in v10.12.5 - using direct Gemini API instead

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyBhZvHQT5Omr8L_O9sn-BFrni6GzPu4pqU",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "sinaik-pwa.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "sinaik-pwa",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "sinaik-pwa.firebasestorage.app",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "801751969697",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:801751969697:web:72cd22232c25d1906265e0",
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-BNLMHV9GG3"
};

// Validate configuration
const validateConfig = () => {
  const required = ['apiKey', 'authDomain', 'projectId'];
  const missing = required.filter(key => !firebaseConfig[key]);

  if (missing.length > 0) {
    console.error('❌ Missing Firebase configuration:', missing);
    throw new Error(`Missing Firebase configuration: ${missing.join(', ')}`);
  }

  console.log('✅ Firebase configuration validated');
  console.log('🔧 Project ID:', firebaseConfig.projectId);
};

// Validate before initializing
validateConfig();

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);

// Add Firestore connection monitoring
import { enableNetwork, connectFirestoreEmulator } from 'firebase/firestore';

// Monitor Firestore connection status
const monitorFirestoreConnection = () => {
  // Check if we're in development and should use emulator
  if (import.meta.env.VITE_USE_FIREBASE_EMULATOR === 'true' && import.meta.env.DEV) {
    try {
      connectFirestoreEmulator(db, 'localhost', 8080);
      console.log('🔧 Connected to Firestore Emulator');
    } catch (error) {
      console.warn('⚠️ Failed to connect to Firestore Emulator:', error);
    }
  }

  // Test Firestore connectivity
  const testConnection = async () => {
    try {
      // Try to enable network explicitly
      await enableNetwork(db);
      console.log('✅ Firestore network enabled successfully');

      // Test a simple operation
      const { doc, getDoc } = await import('firebase/firestore');
      const testDoc = doc(db, 'test', 'connection');
      await getDoc(testDoc);
      console.log('✅ Firestore connection test successful');

    } catch (error) {
      console.error('❌ Firestore connection test failed:', error);
      console.log('🔍 Error details:', {
        code: error.code,
        message: error.message,
        stack: error.stack?.substring(0, 200)
      });

      // Provide specific troubleshooting based on error
      if (error.code === 'unavailable') {
        console.log('💡 Troubleshooting: Firestore service unavailable');
        console.log('   - Check internet connection');
        console.log('   - Verify Firebase project is active');
        console.log('   - Check if Firestore is enabled in Firebase Console');
      } else if (error.code === 'permission-denied') {
        console.log('💡 Troubleshooting: Permission denied');
        console.log('   - Check Firestore security rules');
        console.log('   - Verify user authentication');
      } else if (error.message.includes('offline')) {
        console.log('💡 Troubleshooting: Client detected as offline');
        console.log('   - This may be a CORS or network configuration issue');
        console.log('   - Check browser network tab for failed requests');
        console.log('   - Verify API key permissions in Firebase Console');
      }
    }
  };

  // Run connection test after a short delay
  setTimeout(testConnection, 1000);
};

// Start monitoring
monitorFirestoreConnection();

// Firebase AI not available in v10.12.5
// Using direct Gemini API instead for AI recommendations
console.log('ℹ️ Firebase AI not available in v10.12.5, using direct Gemini API');

console.log('🚀 Firebase initialized successfully');

// AI services not available in Firebase v10.12.5
// Using direct Gemini API in recommendationService.js

export default app;