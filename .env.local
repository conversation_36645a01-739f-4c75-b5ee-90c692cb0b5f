# Firebase Configuration
# Copy this file to .env.local and fill in your Firebase project details

VITE_FIREBASE_API_KEY=AIzaSyBhZvHQT5Omr8L_O9sn-BFrni6GzPu4pqU
VITE_FIREBASE_AUTH_DOMAIN=sinaik-pwa.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=sinaik-pwa
VITE_FIREBASE_STORAGE_BUCKET=sinaik-pwa.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=801751969697
VITE_FIREBASE_APP_ID=1:801751969697:web:1461a75e437828126265e0
VITE_FIREBASE_MEASUREMENT_ID=G-JHY41YCETE

# Optional: Use Firebase Emulator for development
# VITE_USE_FIREBASE_EMULATOR=true

# Google Gemini API Configuration (Direct API)
# Since Firebase v10.12.5 doesn't support Firebase AI, using direct API
VITE_GEMINI_API_KEY=AIzaSyD2ILgdmD_6SuI-ZxmH-dHOCwVyjbS3FcQ

# Gradually re-enabling Firebase functionality
# VITE_DISABLE_FIRESTORE=false

# Keep bypass as fallback during Firebase re-integration
VITE_BYPASS_JSON_PARSING=true
VITE_BYPASS_AI=true

# Firebase re-integration flags
VITE_FIRESTORE_FALLBACK_MODE=true

# Temporary: Disable Firestore to avoid internal assertion errors
# VITE_DISABLE_FIRESTORE=true

# Application Configuration
VITE_APP_NAME=SiNaik PWA
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# Feature Flags
VITE_ENABLE_AI_RECOMMENDATIONS=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_NOTIFICATIONS=true

VITE_ENABLE_AI_TESTING=true
VITE_LOG_AI_RESPONSES=false
VITE_USE_FIREBASE_EMULATOR=true