const a=()=>{console.log("🧪 Testing Vue Router implementation...");const e={testName:"Vue Router Deprecation Fix",passed:!0,issues:[],recommendations:[]};try{const s=document.querySelector("#app");s&&(s.querySelectorAll("router-view").length>0?(console.log("✅ RouterView elements found, checking implementation..."),e.recommendations.push("Modern Vue Router slot props pattern appears to be implemented")):(e.issues.push("No RouterView elements found in DOM"),e.passed=!1)),console.log("ℹ️ Monitor browser console for Vue Router deprecation warnings"),e.recommendations.push('Monitor console for: "Putting <router-view> inside <transition> is deprecated"')}catch(s){e.issues.push(`Router test error: ${s.message}`),e.passed=!1}return e},l=async()=>{console.log("🧪 Testing Firestore connection and error handling...");const e={testName:"Firestore Internal Assertion Fix",passed:!0,issues:[],recommendations:[],details:{}};try{if(typeof window.getFirebaseErrorStats=="function"){const s=window.getFirebaseErrorStats();e.details.errorStats=s,console.log("📊 Firestore Error Stats:",s),s.firestoreHealth.initialized?(console.log("✅ Firestore is initialized and healthy"),e.recommendations.push("Firestore connection is healthy")):(console.log("⚠️ Firestore not initialized or unhealthy"),e.issues.push("Firestore connection not healthy"),s.firestoreHealth.canRetry&&e.recommendations.push("Firestore can be retried - connection recovery available")),s.errorCount>0?(console.log(`⚠️ ${s.errorCount} Firestore errors detected`),e.issues.push(`${s.errorCount} Firestore errors detected`),s.lastError&&(console.log("🔍 Last error:",s.lastError.message),e.details.lastError=s.lastError,s.lastError.message.includes("INTERNAL ASSERTION FAILED")&&e.recommendations.push("Internal assertion error detected - recovery mechanisms should handle this"))):(console.log("✅ No Firestore errors detected"),e.recommendations.push("No Firestore errors - connection is stable"))}else e.issues.push("Firestore debugging functions not available"),e.passed=!1;if(typeof window.testFirestoreConnectivity=="function"){console.log("🔗 Testing Firestore connectivity...");const s=await window.testFirestoreConnectivity();e.details.connectivityTest=s,s?(console.log("✅ Firestore connectivity test passed"),e.recommendations.push("Firestore connectivity test successful")):(console.log("❌ Firestore connectivity test failed"),e.issues.push("Firestore connectivity test failed"))}}catch(s){e.issues.push(`Firestore test error: ${s.message}`),e.passed=!1}return e},c=async()=>{console.log("🧪 Testing AI system functionality...");const e={testName:"AI System Integration",passed:!0,issues:[],recommendations:[],details:{}};try{const s={enabled:!0,bypassMode:!0,hasApiKey:!0,bypassJsonParsing:!0};if(e.details.config=s,console.log("🔧 AI Configuration:",s),s.enabled||(e.issues.push("AI recommendations are disabled"),e.recommendations.push("Enable AI recommendations with VITE_ENABLE_AI_RECOMMENDATIONS=true")),s.bypassMode&&(console.log("ℹ️ AI is in bypass mode - using fallback recommendations"),e.recommendations.push("AI bypass mode is active - set VITE_BYPASS_AI=false for real AI")),!s.hasApiKey&&!s.bypassMode&&(e.issues.push("No Gemini API key configured"),e.recommendations.push("Configure VITE_GEMINI_API_KEY for real AI functionality")),typeof window.quickAITest=="function"){console.log("🤖 Running quick AI test...");try{const t=await window.quickAITest();e.details.aiTest=t,t.success?(console.log("✅ AI test passed"),console.log(`📊 Generated ${t.recommendationCount} recommendations`),e.recommendations.push(`AI test successful - generated ${t.recommendationCount} recommendations`)):(console.log("❌ AI test failed:",t.error),e.issues.push(`AI test failed: ${t.error}`))}catch(t){console.log("❌ AI test threw error:",t.message),e.issues.push(`AI test error: ${t.message}`)}}else e.issues.push("AI test functions not available"),e.recommendations.push("AI test functions should be available in development mode")}catch(s){e.issues.push(`AI system test error: ${s.message}`),e.passed=!1}return e},u=async()=>{console.log("🧪 Testing authentication system...");const e={testName:"Authentication System",passed:!0,issues:[],recommendations:[],details:{}};try{typeof window.firebase<"u"||document.querySelector("[data-firebase]")?(console.log("✅ Firebase appears to be loaded"),e.recommendations.push("Firebase authentication system is available")):(console.log("ℹ️ Firebase not detected in global scope"),e.recommendations.push("Firebase may be loaded via modules (expected in modern setup)"));const s=document.querySelectorAll("[data-auth], .auth-container, .login-form");s.length>0&&(console.log(`✅ Found ${s.length} authentication-related elements`),e.recommendations.push("Authentication UI elements are present")),localStorage.getItem("currentUser")||sessionStorage.getItem("currentUser")?(console.log("ℹ️ User session data found in storage"),e.details.hasUserSession=!0,e.recommendations.push("User session data is preserved")):(console.log("ℹ️ No user session data found"),e.details.hasUserSession=!1)}catch(s){e.issues.push(`Authentication test error: ${s.message}`),e.passed=!1}return e},d=async()=>{console.log("🚀 Running comprehensive system tests..."),console.log("=".repeat(50));const e=[],s=[{name:"Vue Router",test:a},{name:"Firestore",test:l},{name:"AI System",test:c},{name:"Authentication",test:u}];for(const{name:o,test:m}of s){console.log(`
🧪 Running ${o} tests...`);try{const n=await m();e.push(n),n.passed?console.log(`✅ ${o} tests passed`):(console.log(`❌ ${o} tests failed`),n.issues.forEach(i=>console.log(`   - ${i}`))),n.recommendations.length>0&&(console.log(`💡 ${o} recommendations:`),n.recommendations.forEach(i=>console.log(`   - ${i}`)))}catch(n){console.error(`❌ ${o} test suite failed:`,n),e.push({testName:o,passed:!1,issues:[`Test suite error: ${n.message}`],recommendations:[],details:{}})}}console.log(`
`+"=".repeat(50)),console.log("📊 TEST SUMMARY"),console.log("=".repeat(50));const t=e.filter(o=>o.passed).length,r=e.length;return console.log(`✅ Passed: ${t}/${r} test suites`),console.log(t===r?"🎉 All system tests passed!":"⚠️ Some tests failed - check individual results above"),{summary:{total:r,passed:t,failed:r-t,success:t===r},results:e}};typeof window<"u"&&(window.runSystemTests=d,window.testVueRouter=a,window.testFirestore=l,window.testAISystem=c,window.testAuthentication=u,console.log("🧪 System testing functions available:"),console.log("- runSystemTests() - Run all tests"),console.log("- testVueRouter() - Test router implementation"),console.log("- testFirestore() - Test Firestore connection"),console.log("- testAISystem() - Test AI functionality"),console.log("- testAuthentication() - Test auth system"));const g={runSystemTests:d,testVueRouter:a,testFirestore:l,testAISystem:c,testAuthentication:u};export{g as default,d as runSystemTests,c as testAISystem,u as testAuthentication,l as testFirestore,a as testVueRouter};
